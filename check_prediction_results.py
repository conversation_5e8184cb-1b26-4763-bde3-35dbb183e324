#!/usr/bin/env python
"""
检查模型预标注结果
"""

import os
import sys
import django
import json

# 设置 Django 环境
sys.path.append('/Users/<USER>/Documents/Workspace/projects/ggeclabel')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'framework.settings')
django.setup()

from app.models import Task, TaskSample

def check_task_prediction_results():
    """检查任务预标注结果"""
    task_code = "task20250731101326"  # 从日志中看到的任务代码
    
    print(f"=== 检查任务 {task_code} 的预标注结果 ===")
    
    # 获取任务信息
    task = Task.objects.filter(code=task_code).first()
    if not task:
        print(f"❌ 未找到任务: {task_code}")
        return
    
    print(f"✅ 任务名称: {task.name}")
    print(f"📊 样本总数: {task.sample_count}")
    print(f"📝 已标注数: {task.sample_annotation_count}")
    
    # 检查标签列表
    print(f"\n📋 任务标签:")
    if task.labels:
        try:
            labels = json.loads(task.labels)
            print(f"   标签数量: {len(labels)}")
            for i, label in enumerate(labels):
                print(f"   {i+1}. {label.get('labelName', 'Unknown')}: {label.get('labelColor', 'Unknown')}")
        except json.JSONDecodeError:
            print(f"   ❌ 标签格式错误: {task.labels}")
    else:
        print("   📭 无标签")
    
    # 检查样本标注情况
    print(f"\n🔍 样本标注详情:")
    samples = TaskSample.objects.filter(task_code=task_code)
    
    annotated_samples = samples.filter(annotation_state=1)
    unannotated_samples = samples.filter(annotation_state=0)
    
    print(f"   总样本数: {samples.count()}")
    print(f"   已标注: {annotated_samples.count()}")
    print(f"   未标注: {unannotated_samples.count()}")
    
    # 显示一些已标注样本的详情
    if annotated_samples.exists():
        print(f"\n📝 已标注样本示例:")
        for i, sample in enumerate(annotated_samples[:3]):  # 显示前3个
            print(f"   样本 {i+1}: {sample.new_filename}")
            print(f"   标注用户: {sample.annotation_username}")
            print(f"   标注时间: {sample.annotation_time}")
            
            if sample.annotation_content:
                try:
                    annotations = json.loads(sample.annotation_content)
                    print(f"   标注数量: {len(annotations)}")
                    for j, ann in enumerate(annotations[:2]):  # 显示前2个标注
                        # 尝试从不同位置获取标签名称
                        label_name = ann.get('labelName', 'Unknown')
                        if label_name == 'Unknown' and 'labels' in ann:
                            label_name = ann['labels'].get('labelName', 'Unknown')
                        confidence = ann.get('confidence', 'N/A')
                        print(f"     标注 {j+1}: {label_name} (置信度: {confidence})")
                except json.JSONDecodeError:
                    print(f"   ❌ 标注内容格式错误")
            print()
    
    # 检查是否有模型预标注的样本
    model_annotated = samples.filter(
        annotation_state=1,
        annotation_username="模型预标注"
    )
    
    print(f"🤖 模型预标注样本: {model_annotated.count()}")
    
    if model_annotated.exists():
        print("✅ 模型预标注执行成功！")
        
        # 统计标注类别
        all_labels = set()
        total_annotations = 0
        
        for sample in model_annotated:
            if sample.annotation_content:
                try:
                    annotations = json.loads(sample.annotation_content)
                    total_annotations += len(annotations)
                    for ann in annotations:
                        label_name = ann.get('labelName', 'Unknown')
                        if label_name == 'Unknown' and 'labels' in ann:
                            label_name = ann['labels'].get('labelName', 'Unknown')
                        all_labels.add(label_name)
                except:
                    pass
        
        print(f"📊 预标注统计:")
        print(f"   总标注数: {total_annotations}")
        print(f"   使用的标签: {sorted(all_labels)}")
        
    else:
        print("❌ 未找到模型预标注的样本")

def main():
    """主函数"""
    print("🔍 检查模型预标注结果...")
    check_task_prediction_results()

if __name__ == "__main__":
    main()
