#!/usr/bin/env python
"""
修复标注数据格式的脚本
将YOLO预测生成的旧格式标注数据转换为标注系统期望的完整格式
"""

import os
import sys
import django
import json

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ggeclabel.settings')
django.setup()

from app.models import TaskSample


def convert_annotation_format(old_annotation):
    """
    将旧格式的标注数据转换为新格式
    
    旧格式:
    {
        "labelName": "left",
        "labelColor": "#ff0000",
        "content": [...],
        "contentType": "rect",
        "confidence": 0.87185
    }
    
    新格式:
    {
        "content": [...],
        "rectMask": {...},
        "labels": {...},
        "labelLocation": {...},
        "contentType": "rect"
    }
    """
    if not isinstance(old_annotation, dict):
        return old_annotation
    
    # 检查是否已经是新格式（包含labels对象）
    if "labels" in old_annotation and isinstance(old_annotation["labels"], dict):
        return old_annotation
    
    # 检查是否是旧格式（直接包含labelName）
    if "labelName" not in old_annotation or "content" not in old_annotation:
        return old_annotation
    
    content = old_annotation.get("content", [])
    label_name = old_annotation.get("labelName", "未命名")
    label_color = old_annotation.get("labelColor", "#ff0000")
    content_type = old_annotation.get("contentType", "rect")
    confidence = old_annotation.get("confidence", 0.5)
    
    # 计算rectMask和labelLocation
    if content_type == "rect" and len(content) >= 4:
        # 获取边界框坐标
        x_coords = [point["x"] for point in content]
        y_coords = [point["y"] for point in content]
        
        x_min = min(x_coords)
        x_max = max(x_coords)
        y_min = min(y_coords)
        y_max = max(y_coords)
        
        width = x_max - x_min
        height = y_max - y_min
        
        rect_mask = {
            "xMin": x_min,
            "yMin": y_min,
            "width": width,
            "height": height,
        }
        
        label_location = {
            "x": (x_min + x_max) / 2,
            "y": (y_min + y_max) / 2
        }
    else:
        # 对于非矩形或数据不完整的情况，使用默认值
        rect_mask = {
            "xMin": 0,
            "yMin": 0,
            "width": 0,
            "height": 0,
        }
        
        label_location = {
            "x": 0,
            "y": 0
        }
    
    # 转换颜色格式
    label_color_rgb = "255,0,0"  # 默认红色
    if label_color == "#ff0000":
        label_color_rgb = "255,0,0"
    elif label_color == "#00ff00":
        label_color_rgb = "0,255,0"
    elif label_color == "#0000ff":
        label_color_rgb = "0,0,255"
    
    # 构建新格式
    new_annotation = {
        "content": content,
        "rectMask": rect_mask,
        "labels": {
            "labelName": label_name,
            "labelColor": label_color,
            "labelColorRGB": label_color_rgb,
            "visibility": True
        },
        "labelLocation": label_location,
        "contentType": content_type
    }
    
    # 保留confidence字段（如果存在）
    if "confidence" in old_annotation:
        new_annotation["confidence"] = confidence
    
    return new_annotation


def fix_task_annotations(task_code):
    """修复指定任务的标注数据格式"""
    print(f"开始修复任务 {task_code} 的标注数据...")
    
    # 获取任务的所有已标注样本
    samples = TaskSample.objects.filter(
        task_code=task_code,
        annotation_state=1
    ).exclude(annotation_content__isnull=True)
    
    print(f"找到 {len(samples)} 个已标注样本")
    
    fixed_count = 0
    error_count = 0
    
    for sample in samples:
        try:
            # 解析标注内容
            annotation_data = json.loads(sample.annotation_content)
            
            if not isinstance(annotation_data, list):
                print(f"样本 {sample.code}: 标注数据不是列表格式，跳过")
                continue
            
            # 转换每个标注
            new_annotation_data = []
            has_changes = False
            
            for annotation in annotation_data:
                new_annotation = convert_annotation_format(annotation)
                new_annotation_data.append(new_annotation)
                
                # 检查是否有变化
                if new_annotation != annotation:
                    has_changes = True
            
            # 如果有变化，保存到数据库
            if has_changes:
                sample.annotation_content = json.dumps(new_annotation_data)
                sample.save()
                fixed_count += 1
                print(f"样本 {sample.code}: 已修复")
            else:
                print(f"样本 {sample.code}: 格式正确，无需修复")
                
        except Exception as e:
            error_count += 1
            print(f"样本 {sample.code}: 修复失败 - {str(e)}")
    
    print(f"修复完成！成功修复 {fixed_count} 个样本，失败 {error_count} 个样本")
    return fixed_count, error_count


def main():
    """主函数"""
    # 修复任务 venjye_test_0731_1403
    task_code = "task20250731140934"
    
    print("=" * 50)
    print("标注数据格式修复工具")
    print("=" * 50)
    
    fixed_count, error_count = fix_task_annotations(task_code)
    
    print("\n" + "=" * 50)
    print("修复完成！")
    print(f"成功修复: {fixed_count} 个样本")
    print(f"修复失败: {error_count} 个样本")
    print("=" * 50)


if __name__ == "__main__":
    main()
